import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';

// Layout Components
import MainLayout from './components/layout/MainLayout';
import AuthLayout from './components/layout/AuthLayout';

// Page Components
import Dashboard from './pages/Dashboard';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import NotFound from './pages/NotFound';

// Route Components
import CustomerRoutes from './pages/customers/CustomerRoutes';
import ServiceRoutes from './pages/services/ServiceRoutes';

// Hooks
import { useAuth } from './hooks/useAuth';

// Utils
import { ProtectedRoute } from './utils/ProtectedRoute';

// Styles
import './styles/App.css';

function App() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>TallyCRM - CRM for Tally Resellers</title>
        <meta name="description" content="Complete CRM solution for Tally Software Resellers" />
      </Helmet>

      <div className="App">
        <Routes>
          {/* Public Routes */}
          <Route path="/auth" element={<AuthLayout />}>
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
            <Route path="forgot-password" element={<ForgotPassword />} />
            <Route index element={<Navigate to="login" replace />} />
          </Route>

          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />

            {/* Customer Management Routes */}
            <Route path="customers/*" element={<CustomerRoutes />} />

            {/* Service Management Routes */}
            <Route path="services/*" element={<ServiceRoutes />} />

            {/* Sales Management Routes */}
            <Route path="sales/*" element={<div>Sales Management (Coming Soon)</div>} />

            {/* Masters Routes */}
            <Route path="masters/*" element={<div>Masters Management (Coming Soon)</div>} />

            {/* Reports Routes */}
            <Route path="reports/*" element={<div>Reports & Analytics (Coming Soon)</div>} />

            {/* Settings Routes */}
            <Route path="settings/*" element={<div>Settings (Coming Soon)</div>} />
          </Route>

          {/* Redirect root to dashboard if authenticated, otherwise to login */}
          <Route
            path="/"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <Navigate to="/auth/login" replace />
              )
            }
          />

          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              theme: {
                primary: 'green',
                secondary: 'black',
              },
            },
          }}
        />
      </div>
    </>
  );
}

export default App;
