import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';

const testDatabase = async () => {
  try {
    logger.info('Testing database connection...');
    
    // Test connection
    await models.sequelize.authenticate();
    logger.info('✅ Database connection successful');
    
    // Test if tables exist
    const tables = await models.sequelize.getQueryInterface().showAllTables();
    logger.info('📋 Available tables:', tables);
    
    // Test Role model
    const roleCount = await models.Role.count();
    logger.info(`📊 Roles count: ${roleCount}`);
    
    // Test if we can create a role
    const testRole = await models.Role.findOrCreate({
      where: { slug: 'test_role' },
      defaults: {
        name: 'Test Role',
        slug: 'test_role',
        description: 'Test role for database testing',
        is_system: false,
        level: 10
      }
    });
    
    logger.info('✅ Test role created/found:', testRole[0].name);
    
    // Clean up test role
    await models.Role.destroy({ where: { slug: 'test_role' } });
    logger.info('🧹 Test role cleaned up');
    
    logger.info('✅ Database test completed successfully!');
    
  } catch (error) {
    logger.error('❌ Database test failed:', error);
  } finally {
    await models.sequelize.close();
  }
};

testDatabase();
