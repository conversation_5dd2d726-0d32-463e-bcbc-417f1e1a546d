import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>a<PERSON><PERSON>, 
  Fa<PERSON>hield,
  FaBell,
  FaEnvelope,
  FaDatabase,
  FaPalette,
  FaGlobe,
  FaSave,
  FaEdit,
  FaCheck,
  FaTimes
} from 'react-icons/fa';

const SettingsList = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [editingField, setEditingField] = useState(null);
  const [settings, setSettings] = useState({
    // General Settings
    companyName: 'TallyCRM Solutions',
    companyEmail: '<EMAIL>',
    companyPhone: '+91 9876543210',
    companyAddress: '123 Business Park, Mumbai, Maharashtra',
    timezone: 'Asia/Kolkata',
    dateFormat: 'DD/MM/YYYY',
    currency: 'INR',
    language: 'English',
    
    // User Preferences
    defaultDashboard: 'overview',
    itemsPerPage: 10,
    autoSave: true,
    showNotifications: true,
    darkMode: false,
    
    // Security Settings
    sessionTimeout: 30,
    passwordExpiry: 90,
    twoFactorAuth: false,
    loginAttempts: 5,
    
    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyReports: true,
    monthlyReports: true,
    
    // Email Settings
    smtpServer: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '<EMAIL>',
    smtpPassword: '••••••••',
    emailSignature: 'Best regards,\nTallyCRM Team',
    
    // Database Settings
    backupFrequency: 'daily',
    retentionPeriod: 365,
    autoBackup: true,
    
    // Appearance Settings
    primaryColor: '#007bff',
    secondaryColor: '#6c757d',
    logoUrl: '/assets/logo.png',
    favicon: '/assets/favicon.ico'
  });

  const settingsTabs = [
    { id: 'general', name: 'General', icon: <FaCog /> },
    { id: 'user', name: 'User Preferences', icon: <FaUser /> },
    { id: 'security', name: 'Security', icon: <FaShield /> },
    { id: 'notifications', name: 'Notifications', icon: <FaBell /> },
    { id: 'email', name: 'Email', icon: <FaEnvelope /> },
    { id: 'database', name: 'Database', icon: <FaDatabase /> },
    { id: 'appearance', name: 'Appearance', icon: <FaPalette /> }
  ];

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = (key) => {
    setEditingField(null);
    toast.success('Setting updated successfully');
  };

  const handleCancel = () => {
    setEditingField(null);
  };

  const renderSettingField = (key, label, type = 'text', options = null) => {
    const isEditing = editingField === key;
    const value = settings[key];

    return (
      <div className="row mb-3">
        <div className="col-md-4">
          <label className="form-label fw-bold">{label}</label>
        </div>
        <div className="col-md-6">
          {isEditing ? (
            <>
              {type === 'select' ? (
                <select
                  className="form-select"
                  value={value}
                  onChange={(e) => handleSettingChange(key, e.target.value)}
                >
                  {options.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : type === 'checkbox' ? (
                <div className="form-check">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={value}
                    onChange={(e) => handleSettingChange(key, e.target.checked)}
                  />
                  <label className="form-check-label">
                    {value ? 'Enabled' : 'Disabled'}
                  </label>
                </div>
              ) : type === 'textarea' ? (
                <textarea
                  className="form-control"
                  rows="3"
                  value={value}
                  onChange={(e) => handleSettingChange(key, e.target.value)}
                />
              ) : type === 'color' ? (
                <input
                  type="color"
                  className="form-control form-control-color"
                  value={value}
                  onChange={(e) => handleSettingChange(key, e.target.value)}
                />
              ) : (
                <input
                  type={type}
                  className="form-control"
                  value={value}
                  onChange={(e) => handleSettingChange(key, e.target.value)}
                />
              )}
            </>
          ) : (
            <div className="form-control-plaintext">
              {type === 'checkbox' ? (
                <span className={`badge ${value ? 'bg-success' : 'bg-secondary'}`}>
                  {value ? 'Enabled' : 'Disabled'}
                </span>
              ) : type === 'color' ? (
                <div className="d-flex align-items-center">
                  <div 
                    className="me-2" 
                    style={{ 
                      width: '20px', 
                      height: '20px', 
                      backgroundColor: value, 
                      border: '1px solid #ccc' 
                    }}
                  ></div>
                  {value}
                </div>
              ) : type === 'password' ? (
                '••••••••'
              ) : (
                value
              )}
            </div>
          )}
        </div>
        <div className="col-md-2">
          {isEditing ? (
            <div className="btn-group" role="group">
              <button 
                className="btn btn-sm btn-success"
                onClick={() => handleSave(key)}
              >
                <FaCheck />
              </button>
              <button 
                className="btn btn-sm btn-secondary"
                onClick={handleCancel}
              >
                <FaTimes />
              </button>
            </div>
          ) : (
            <button 
              className="btn btn-sm btn-outline-primary"
              onClick={() => setEditingField(key)}
            >
              <FaEdit />
            </button>
          )}
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div>
            <h5 className="mb-4">General Settings</h5>
            {renderSettingField('companyName', 'Company Name')}
            {renderSettingField('companyEmail', 'Company Email', 'email')}
            {renderSettingField('companyPhone', 'Company Phone', 'tel')}
            {renderSettingField('companyAddress', 'Company Address', 'textarea')}
            {renderSettingField('timezone', 'Timezone', 'select', [
              { value: 'Asia/Kolkata', label: 'Asia/Kolkata (IST)' },
              { value: 'UTC', label: 'UTC' },
              { value: 'America/New_York', label: 'America/New_York (EST)' }
            ])}
            {renderSettingField('dateFormat', 'Date Format', 'select', [
              { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
              { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
              { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' }
            ])}
            {renderSettingField('currency', 'Default Currency', 'select', [
              { value: 'INR', label: 'Indian Rupee (INR)' },
              { value: 'USD', label: 'US Dollar (USD)' },
              { value: 'EUR', label: 'Euro (EUR)' }
            ])}
            {renderSettingField('language', 'Language', 'select', [
              { value: 'English', label: 'English' },
              { value: 'Hindi', label: 'Hindi' },
              { value: 'Marathi', label: 'Marathi' }
            ])}
          </div>
        );

      case 'user':
        return (
          <div>
            <h5 className="mb-4">User Preferences</h5>
            {renderSettingField('defaultDashboard', 'Default Dashboard', 'select', [
              { value: 'overview', label: 'Overview' },
              { value: 'sales', label: 'Sales Dashboard' },
              { value: 'services', label: 'Services Dashboard' }
            ])}
            {renderSettingField('itemsPerPage', 'Items Per Page', 'number')}
            {renderSettingField('autoSave', 'Auto Save', 'checkbox')}
            {renderSettingField('showNotifications', 'Show Notifications', 'checkbox')}
            {renderSettingField('darkMode', 'Dark Mode', 'checkbox')}
          </div>
        );

      case 'security':
        return (
          <div>
            <h5 className="mb-4">Security Settings</h5>
            {renderSettingField('sessionTimeout', 'Session Timeout (minutes)', 'number')}
            {renderSettingField('passwordExpiry', 'Password Expiry (days)', 'number')}
            {renderSettingField('twoFactorAuth', 'Two-Factor Authentication', 'checkbox')}
            {renderSettingField('loginAttempts', 'Max Login Attempts', 'number')}
          </div>
        );

      case 'notifications':
        return (
          <div>
            <h5 className="mb-4">Notification Settings</h5>
            {renderSettingField('emailNotifications', 'Email Notifications', 'checkbox')}
            {renderSettingField('smsNotifications', 'SMS Notifications', 'checkbox')}
            {renderSettingField('pushNotifications', 'Push Notifications', 'checkbox')}
            {renderSettingField('weeklyReports', 'Weekly Reports', 'checkbox')}
            {renderSettingField('monthlyReports', 'Monthly Reports', 'checkbox')}
          </div>
        );

      case 'email':
        return (
          <div>
            <h5 className="mb-4">Email Settings</h5>
            {renderSettingField('smtpServer', 'SMTP Server')}
            {renderSettingField('smtpPort', 'SMTP Port', 'number')}
            {renderSettingField('smtpUsername', 'SMTP Username')}
            {renderSettingField('smtpPassword', 'SMTP Password', 'password')}
            {renderSettingField('emailSignature', 'Email Signature', 'textarea')}
          </div>
        );

      case 'database':
        return (
          <div>
            <h5 className="mb-4">Database Settings</h5>
            {renderSettingField('backupFrequency', 'Backup Frequency', 'select', [
              { value: 'daily', label: 'Daily' },
              { value: 'weekly', label: 'Weekly' },
              { value: 'monthly', label: 'Monthly' }
            ])}
            {renderSettingField('retentionPeriod', 'Retention Period (days)', 'number')}
            {renderSettingField('autoBackup', 'Auto Backup', 'checkbox')}
          </div>
        );

      case 'appearance':
        return (
          <div>
            <h5 className="mb-4">Appearance Settings</h5>
            {renderSettingField('primaryColor', 'Primary Color', 'color')}
            {renderSettingField('secondaryColor', 'Secondary Color', 'color')}
            {renderSettingField('logoUrl', 'Logo URL')}
            {renderSettingField('favicon', 'Favicon URL')}
          </div>
        );

      default:
        return <div>Select a settings category</div>;
    }
  };

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">Settings</h2>
              <p className="text-muted">Manage system settings and preferences</p>
            </div>
            <button className="btn btn-primary">
              <FaSave className="me-2" />
              Save All Changes
            </button>
          </div>
        </div>
      </div>

      <div className="row">
        {/* Settings Navigation */}
        <div className="col-lg-3 mb-4">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">Settings Categories</h5>
            </div>
            <div className="card-body p-0">
              <div className="list-group list-group-flush">
                {settingsTabs.map(tab => (
                  <button
                    key={tab.id}
                    className={`list-group-item list-group-item-action d-flex align-items-center ${
                      activeTab === tab.id ? 'active' : ''
                    }`}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.icon}
                    <span className="ms-2">{tab.name}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Settings Content */}
        <div className="col-lg-9">
          <div className="card">
            <div className="card-body">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsList;
